#!/bin/bash

# Test ABDM Webhook Connectivity
WEBHOOK_URL="https://810af3efa697.ngrok-free.app/api/abdm/webhook"

echo "🧪 Testing ABDM Webhook Connectivity..."
echo "📍 URL: $WEBHOOK_URL"
echo ""

# Test 1: GET request (verification)
echo "1️⃣ Testing GET request (webhook verification)..."
curl -X GET "$WEBHOOK_URL" \
  -H "Accept: application/json" \
  -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
  -s

echo ""

# Test 2: GET with challenge parameter
echo "2️⃣ Testing GET with challenge parameter..."
curl -X GET "$WEBHOOK_URL?challenge=test123&mode=subscribe" \
  -H "Accept: application/json" \
  -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
  -s

echo ""

# Test 3: POST request (webhook notification)
echo "3️⃣ Testing POST request (webhook notification)..."
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-ABDM-Signature: test_signature" \
  -H "X-Timestamp: $(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)" \
  -H "X-Request-ID: test_$(date +%s)" \
  -d '{
    "eventType": "ABHA_CREATED",
    "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
    "requestId": "test_'$(date +%s)'",
    "data": {
      "abhaNumber": "12-3456-7890-1234",
      "abhaAddress": "test.user@abdm",
      "mobile": "9876543210"
    }
  }' \
  -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
  -s

echo ""

# Test 4: Check if ngrok is running
echo "4️⃣ Checking ngrok status..."
if command -v ngrok &> /dev/null; then
    echo "✅ ngrok is installed"
    # Try to get ngrok status
    curl -s http://localhost:4040/api/tunnels 2>/dev/null | grep -o '"public_url":"[^"]*"' | head -1 || echo "❌ ngrok might not be running on port 4040"
else
    echo "❌ ngrok not found in PATH"
fi

echo ""
echo "🔍 Troubleshooting Tips:"
echo "1. Make sure your Azure Function is running locally (func start)"
echo "2. Make sure ngrok is running and pointing to your function port"
echo "3. Check that the ngrok URL is correct and accessible"
echo "4. Verify your function logs for any errors"
echo ""
echo "📋 Next steps:"
echo "1. If tests pass, re-register the webhook with ABDM using the corrected URL"
echo "2. Monitor your function logs when creating ABHA numbers"
echo "3. Check ABDM documentation for specific webhook event triggers"

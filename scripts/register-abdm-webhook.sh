#!/bin/bash

# ABDM Webhook Registration Script
# This script registers your webhook URL with ABDM

# Configuration
ABDM_BASE_URL="https://dev.abdm.gov.in/api/hiecm/gateway/v3"
WEBHOOK_URL="https://810af3efa697.ngrok-free.app/api/abdm/webhook"  # Fixed: single slash
REQUEST_ID=$(uuidgen)
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
X_CM_ID="sbx"

# Get your current token (you'll need to update this)
AUTH_TOKEN="eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hNmKMu_EICXYE7uk3haquAUwNxfg-gl8np5ZRbLi4XkzZuG_Gh-MQ0uslvC-LFs70-JksSyXH3UEoonGaHlh7Wk_XIcUfuwERGokTEv-0h5eb6XTmIW8YoYc9_X1IHTyHERcMv9TPvHnTM58-pP7cfA_nLI1oovumV3xVPntoFeoS4K9S1-jQe2nJEUCSdD9gGhBixbbaaY9n4WGDZZCTb315SGOqWeerinQ1w_n2_FmFJlf8BPG3rYP26ZzlVVsFxnSXv-B-C1W69wpu0QTdsPfEPhe0X2nPcIdASYy-B66jsjkJejNtTlhduUkPC258Nhac65RvLFxVxMDFaTFtg"

echo "🔧 Registering ABDM Webhook..."
echo "📍 Webhook URL: $WEBHOOK_URL"
echo "🆔 Request ID: $REQUEST_ID"
echo "⏰ Timestamp: $TIMESTAMP"

# Register the webhook URL
curl --location --request PATCH "$ABDM_BASE_URL/bridge/url" \
  --header "REQUEST-ID: $REQUEST_ID" \
  --header "TIMESTAMP: $TIMESTAMP" \
  --header "X-CM-ID: $X_CM_ID" \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer $AUTH_TOKEN" \
  --data "{
    \"url\": \"$WEBHOOK_URL\"
  }" \
  --verbose

echo ""
echo "✅ Webhook registration completed!"
echo ""
echo "🧪 Test your webhook with:"
echo "curl -X POST $WEBHOOK_URL \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"eventType\": \"test\", \"data\": {\"message\": \"test\"}}'"

/**
 * ABDM Webhook Registration Script
 * Comprehensive script to register webhooks with ABDM
 */

const abdmWebhookRegistration = require('../src/services/abdm-webhook-registration')

class WebhookRegistrationManager {
  constructor() {
    this.defaultWebhookUrl = process.env.WEBHOOK_URL || 'https://810af3efa697.ngrok-free.app/api/abdm/webhook'
  }

  /**
   * Register webhooks with ABDM
   */
  async registerWebhooks(webhookUrl = null) {
    const url = webhookUrl || this.defaultWebhookUrl
    
    console.log('🚀 ABDM Webhook Registration')
    console.log('============================')
    console.log(`📍 Webhook URL: ${url}`)
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`)
    console.log('')

    try {
      // Step 1: Verify webhook endpoint
      console.log('1️⃣ Verifying webhook endpoint...')
      const verification = await abdmWebhookRegistration.verifyWebhookRegistration(url)
      
      if (verification.accessible) {
        console.log('✅ Webhook endpoint is accessible')
        console.log(`📊 Status: ${verification.status}`)
        if (verification.data) {
          console.log('📄 Response:', JSON.stringify(verification.data, null, 2))
        }
      } else {
        console.log('❌ Webhook endpoint is not accessible')
        console.log('💡 Make sure your webhook is running and accessible')
        return
      }

      console.log('')

      // Step 2: Register webhooks
      console.log('2️⃣ Registering webhooks with ABDM...')
      const results = await abdmWebhookRegistration.registerAllWebhooks(url)

      if (results.error) {
        console.log('❌ Registration failed:', results.error)
        return
      }

      // Step 3: Display results
      console.log('')
      console.log('📊 Registration Results:')
      console.log('========================')
      
      if (results.bridgeUrl) {
        console.log(`🌉 Bridge URL: ${results.bridgeUrl.success ? '✅ Success' : '❌ Failed'}`)
        if (results.bridgeUrl.success) {
          console.log('   Purpose: Health Information Exchange (HIE) events')
          console.log('   Events: Consent requests, data sharing, etc.')
        } else {
          console.log(`   Error: ${results.bridgeUrl.error}`)
        }
      }

      if (results.abhaWebhook) {
        console.log(`🆔 ABHA Webhook: ${results.abhaWebhook.success ? '✅ Success' : '❌ Failed'}`)
        if (results.abhaWebhook.success) {
          console.log('   Purpose: ABHA lifecycle events')
          console.log('   Events: ABHA creation, updates, verification')
        } else {
          console.log(`   Error: ${results.abhaWebhook.error}`)
        }
      }

      console.log('')

      // Step 4: Next steps
      console.log('🎯 Next Steps:')
      console.log('==============')
      
      if (results.bridgeUrl?.success) {
        console.log('✅ Bridge URL registered - you will receive HIE events')
      }
      
      if (results.abhaWebhook?.success) {
        console.log('✅ ABHA webhook registered - you will receive ABHA events')
      } else {
        console.log('💡 ABHA webhook not available - using internal webhook triggers')
        console.log('   Your ABHA operations will trigger internal webhooks automatically')
      }

      console.log('')
      console.log('🧪 Test your setup:')
      console.log('- Run: npm run test:abdm')
      console.log('- Monitor function logs during ABHA operations')
      console.log('- Check for webhook processing messages')

    } catch (error) {
      console.error('❌ Registration error:', error.message)
    }
  }

  /**
   * Check current webhook status
   */
  async checkStatus() {
    console.log('🔍 Checking ABDM Webhook Status')
    console.log('===============================')

    try {
      const authToken = await abdmWebhookRegistration.getAuthToken()
      const status = await abdmWebhookRegistration.getWebhookStatus(authToken)

      if (status.success) {
        console.log('📊 Current Status:', JSON.stringify(status.data, null, 2))
      } else {
        console.log('❌ Failed to get status:', status.error)
      }
    } catch (error) {
      console.error('❌ Status check error:', error.message)
    }
  }

  /**
   * Test webhook endpoint
   */
  async testEndpoint(webhookUrl = null) {
    const url = webhookUrl || this.defaultWebhookUrl
    
    console.log('🧪 Testing Webhook Endpoint')
    console.log('===========================')
    console.log(`📍 URL: ${url}`)

    const verification = await abdmWebhookRegistration.verifyWebhookRegistration(url)
    
    if (verification.accessible) {
      console.log('✅ Endpoint is accessible')
      console.log(`📊 Status: ${verification.status}`)
      console.log('📄 Response:', JSON.stringify(verification.data, null, 2))
    } else {
      console.log('❌ Endpoint is not accessible')
      console.log(`❌ Error: ${verification.error}`)
    }
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log('🔧 ABDM Webhook Registration Tool')
    console.log('=================================')
    console.log('')
    console.log('Usage:')
    console.log('  node scripts/register-webhook.js [command] [webhook-url]')
    console.log('')
    console.log('Commands:')
    console.log('  register  - Register webhook with ABDM (default)')
    console.log('  status    - Check current webhook status')
    console.log('  test      - Test webhook endpoint accessibility')
    console.log('  help      - Show this help message')
    console.log('')
    console.log('Examples:')
    console.log('  node scripts/register-webhook.js')
    console.log('  node scripts/register-webhook.js register https://your-domain.com/api/abdm/webhook')
    console.log('  node scripts/register-webhook.js status')
    console.log('  node scripts/register-webhook.js test')
    console.log('')
    console.log('Environment Variables:')
    console.log('  WEBHOOK_URL - Default webhook URL to register')
    console.log('  ABDM_CLIENT_ID - ABDM client ID')
    console.log('  ABDM_CLIENT_SECRET - ABDM client secret')
    console.log('')
    console.log('Current Configuration:')
    console.log(`  Webhook URL: ${this.defaultWebhookUrl}`)
    console.log(`  Client ID: ${process.env.ABDM_CLIENT_ID || 'SBXID_009193'}`)
  }
}

// Main execution
async function main() {
  const manager = new WebhookRegistrationManager()
  const args = process.argv.slice(2)
  
  const command = args[0] || 'register'
  const webhookUrl = args[1] || null

  switch (command.toLowerCase()) {
    case 'register':
      await manager.registerWebhooks(webhookUrl)
      break
    case 'status':
      await manager.checkStatus()
      break
    case 'test':
      await manager.testEndpoint(webhookUrl)
      break
    case 'help':
    case '--help':
    case '-h':
      manager.showHelp()
      break
    default:
      console.log(`❌ Unknown command: ${command}`)
      console.log('💡 Run with "help" to see available commands')
      manager.showHelp()
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = WebhookRegistrationManager

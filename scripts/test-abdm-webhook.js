/**
 * ABDM Webhook Test Script
 * This script helps test the ABDM webhook endpoint with various event types
 */

const crypto = require('crypto')
const axios = require('axios')

class ABDMWebhookTester {
  constructor(webhookUrl, webhookSecret) {
    this.webhookUrl = webhookUrl
    this.webhookSecret = webhookSecret
  }

  /**
   * Generate HMAC signature for webhook payload
   * @param {Object} payload - Webhook payload
   * @returns {string} - HMAC signature
   */
  generateSignature(payload) {
    if (!this.webhookSecret) {
      console.warn('No webhook secret provided, skipping signature generation')
      return 'test_signature'
    }

    return crypto
      .createHmac('sha256', this.webhookSecret)
      .update(JSON.stringify(payload))
      .digest('hex')
  }

  /**
   * Send webhook test request
   * @param {Object} payload - Webhook payload
   * @returns {Promise} - Response from webhook
   */
  async sendWebhook(payload) {
    const signature = this.generateSignature(payload)
    const timestamp = new Date().toISOString()
    const requestId = `test_${Date.now()}`

    const headers = {
      'Content-Type': 'application/json',
      'X-ABDM-Signature': signature,
      'X-Timestamp': timestamp,
      'X-Request-ID': requestId
    }

    try {
      console.log(`\n🚀 Sending webhook: ${payload.eventType}`)
      console.log(`📍 URL: ${this.webhookUrl}`)
      console.log(`🔑 Request ID: ${requestId}`)
      
      const response = await axios.post(this.webhookUrl, payload, { headers })
      
      console.log(`✅ Success: ${response.status} ${response.statusText}`)
      console.log(`📄 Response:`, response.data)
      
      return response.data
    } catch (error) {
      console.error(`❌ Error: ${error.message}`)
      if (error.response) {
        console.error(`📄 Response:`, error.response.data)
      }
      throw error
    }
  }

  /**
   * Test ABHA Created event
   */
  async testAbhaCreated() {
    const payload = {
      eventType: 'ABHA_CREATED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        abhaNumber: '12-3456-7890-1234',
        abhaAddress: 'test.user@abdm',
        mobile: '9876543210',
        email: '<EMAIL>',
        txnId: `txn_${Date.now()}`
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test ABHA Updated event
   */
  async testAbhaUpdated() {
    const payload = {
      eventType: 'ABHA_UPDATED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        abhaNumber: '12-3456-7890-1234',
        updatedFields: {
          mobile: '9876543211',
          email: '<EMAIL>'
        }
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test ABHA Verified event
   */
  async testAbhaVerified() {
    const payload = {
      eventType: 'ABHA_VERIFIED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        abhaNumber: '12-3456-7890-1234',
        verificationStatus: 'VERIFIED',
        verifiedAt: new Date().toISOString()
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test Consent Granted event
   */
  async testConsentGranted() {
    const payload = {
      eventType: 'CONSENT_GRANTED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        consentId: `consent_${Date.now()}`,
        abhaNumber: '12-3456-7890-1234',
        purpose: 'CAREMGT',
        dataTypes: ['Prescription', 'DiagnosticReport', 'DischargeSummary'],
        validFrom: new Date().toISOString(),
        validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test Consent Revoked event
   */
  async testConsentRevoked() {
    const payload = {
      eventType: 'CONSENT_REVOKED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        consentId: `consent_${Date.now()}`,
        abhaNumber: '12-3456-7890-1234',
        reason: 'Patient requested revocation',
        revokedAt: new Date().toISOString()
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test Health Record Shared event
   */
  async testHealthRecordShared() {
    const payload = {
      eventType: 'HEALTH_RECORD_SHARED',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        recordId: `record_${Date.now()}`,
        abhaNumber: '12-3456-7890-1234',
        sharedWith: 'hospital_123',
        recordType: 'Prescription',
        sharedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Test unknown event type
   */
  async testUnknownEvent() {
    const payload = {
      eventType: 'UNKNOWN_EVENT',
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
      data: {
        message: 'This is an unknown event type for testing'
      }
    }

    return await this.sendWebhook(payload)
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting ABDM Webhook Tests...\n')

    const tests = [
      { name: 'ABHA Created', fn: () => this.testAbhaCreated() },
      { name: 'ABHA Updated', fn: () => this.testAbhaUpdated() },
      { name: 'ABHA Verified', fn: () => this.testAbhaVerified() },
      { name: 'Consent Granted', fn: () => this.testConsentGranted() },
      { name: 'Consent Revoked', fn: () => this.testConsentRevoked() },
      { name: 'Health Record Shared', fn: () => this.testHealthRecordShared() },
      { name: 'Unknown Event', fn: () => this.testUnknownEvent() }
    ]

    const results = []

    for (const test of tests) {
      try {
        console.log(`\n📋 Running test: ${test.name}`)
        const result = await test.fn()
        results.push({ name: test.name, status: 'PASSED', result })
        await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second between tests
      } catch (error) {
        results.push({ name: test.name, status: 'FAILED', error: error.message })
      }
    }

    // Print summary
    console.log('\n📊 Test Summary:')
    console.log('================')
    results.forEach(result => {
      const status = result.status === 'PASSED' ? '✅' : '❌'
      console.log(`${status} ${result.name}: ${result.status}`)
      if (result.error) {
        console.log(`   Error: ${result.error}`)
      }
    })

    const passed = results.filter(r => r.status === 'PASSED').length
    const total = results.length
    console.log(`\n🎯 Results: ${passed}/${total} tests passed`)

    return results
  }
}

// Usage example
async function main() {
  // Configuration
  const WEBHOOK_URL = process.env.WEBHOOK_URL || 'http://localhost:7071/api/abdm/webhook'
  const WEBHOOK_SECRET = process.env.ABDM_WEBHOOK_SECRET || 'test-secret'

  console.log('🔧 Configuration:')
  console.log(`   Webhook URL: ${WEBHOOK_URL}`)
  console.log(`   Webhook Secret: ${WEBHOOK_SECRET ? '***configured***' : 'not configured'}`)

  const tester = new ABDMWebhookTester(WEBHOOK_URL, WEBHOOK_SECRET)

  // Check command line arguments
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    // Run all tests
    await tester.runAllTests()
  } else {
    // Run specific test
    const testName = args[0].toLowerCase()
    
    switch (testName) {
      case 'created':
        await tester.testAbhaCreated()
        break
      case 'updated':
        await tester.testAbhaUpdated()
        break
      case 'verified':
        await tester.testAbhaVerified()
        break
      case 'consent-granted':
        await tester.testConsentGranted()
        break
      case 'consent-revoked':
        await tester.testConsentRevoked()
        break
      case 'record-shared':
        await tester.testHealthRecordShared()
        break
      case 'unknown':
        await tester.testUnknownEvent()
        break
      default:
        console.log('❌ Unknown test name. Available tests:')
        console.log('   - created')
        console.log('   - updated')
        console.log('   - verified')
        console.log('   - consent-granted')
        console.log('   - consent-revoked')
        console.log('   - record-shared')
        console.log('   - unknown')
        console.log('\n   Or run without arguments to run all tests')
    }
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = ABDMWebhookTester

/**
 * ABDM Webhook Registration Service
 * Handles registration of webhook URLs with ABDM for different event types
 */

const axios = require('axios')
const { logError, logInfo } = require('../common/logging')

class ABDMWebhookRegistration {
  constructor() {
    this.baseUrl = process.env.ABDM_BASE_URL || 'https://abhasbx.abdm.gov.in/abha/api/v3'
    this.gatewayUrl = process.env.ABDM_GATEWAY_URL || 'https://dev.abdm.gov.in/api/hiecm/gateway/v3'
    this.clientId = process.env.ABDM_CLIENT_ID || 'SBXID_009193'
    this.clientSecret = process.env.ABDM_CLIENT_SECRET || '4df7c42b-46c0-4b1b-95b6-35c6a5317708'
  }

  /**
   * Generate request ID for ABDM API calls
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get authentication token for ABDM API
   */
  async getAuthToken() {
    try {
      logInfo('Getting ABDM authentication token...')
      
      const response = await axios.post(`${this.gatewayUrl}/sessions`, {
        clientId: this.clientId,
        clientSecret: this.clientSecret
      }, {
        headers: {
          'Content-Type': 'application/json',
          'REQUEST-ID': this.generateRequestId(),
          'TIMESTAMP': new Date().toISOString()
        }
      })

      if (response.data && response.data.accessToken) {
        logInfo('✅ ABDM authentication token obtained')
        return response.data.accessToken
      } else {
        throw new Error('No access token in response')
      }
    } catch (error) {
      logError('Failed to get ABDM auth token:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Register bridge URL for HIE webhooks
   */
  async registerBridgeUrl(webhookUrl, authToken) {
    try {
      logInfo(`Registering bridge URL: ${webhookUrl}`)
      
      const response = await axios.patch(`${this.gatewayUrl}/bridge/url`, {
        url: webhookUrl
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          'REQUEST-ID': this.generateRequestId(),
          'TIMESTAMP': new Date().toISOString(),
          'X-CM-ID': 'sbx'
        }
      })

      logInfo('✅ Bridge URL registered successfully:', response.data)
      return { success: true, data: response.data }
    } catch (error) {
      logError('Failed to register bridge URL:', error.response?.data || error.message)
      return { success: false, error: error.response?.data || error.message }
    }
  }

  /**
   * Register ABHA webhook (if endpoint exists)
   */
  async registerAbhaWebhook(webhookUrl, authToken, events = []) {
    try {
      logInfo(`Attempting to register ABHA webhook: ${webhookUrl}`)
      
      // Try different possible endpoints for ABHA webhook registration
      const possibleEndpoints = [
        '/webhooks/register',
        '/abha/webhooks',
        '/notifications/webhook',
        '/events/webhook'
      ]

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await axios.post(`${this.baseUrl}${endpoint}`, {
            url: webhookUrl,
            events: events.length > 0 ? events : [
              'ABHA_CREATED',
              'ABHA_UPDATED',
              'ABHA_VERIFIED'
            ]
          }, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
              'REQUEST-ID': this.generateRequestId(),
              'TIMESTAMP': new Date().toISOString()
            }
          })

          logInfo(`✅ ABHA webhook registered via ${endpoint}:`, response.data)
          return { success: true, endpoint, data: response.data }
        } catch (error) {
          logInfo(`❌ Endpoint ${endpoint} not available:`, error.response?.status)
          continue
        }
      }

      logInfo('❌ No ABHA webhook endpoints found')
      return { success: false, error: 'No ABHA webhook endpoints available' }
    } catch (error) {
      logError('Failed to register ABHA webhook:', error.message)
      return { success: false, error: error.message }
    }
  }

  /**
   * Register all webhook types
   */
  async registerAllWebhooks(webhookUrl) {
    try {
      logInfo(`🔧 Starting webhook registration for: ${webhookUrl}`)
      
      // Get authentication token
      const authToken = await this.getAuthToken()
      
      const results = {
        bridgeUrl: null,
        abhaWebhook: null
      }

      // Register bridge URL (for HIE events)
      logInfo('📋 Registering bridge URL...')
      results.bridgeUrl = await this.registerBridgeUrl(webhookUrl, authToken)

      // Register ABHA webhook (if available)
      logInfo('📋 Registering ABHA webhook...')
      results.abhaWebhook = await this.registerAbhaWebhook(webhookUrl, authToken)

      // Summary
      logInfo('📊 Webhook Registration Summary:')
      logInfo(`- Bridge URL: ${results.bridgeUrl.success ? '✅ Success' : '❌ Failed'}`)
      logInfo(`- ABHA Webhook: ${results.abhaWebhook.success ? '✅ Success' : '❌ Failed'}`)

      return results
    } catch (error) {
      logError('Error in webhook registration:', error)
      return { error: error.message }
    }
  }

  /**
   * Verify webhook registration
   */
  async verifyWebhookRegistration(webhookUrl) {
    try {
      logInfo(`🔍 Verifying webhook registration: ${webhookUrl}`)
      
      // Test webhook endpoint accessibility
      const response = await axios.get(webhookUrl, {
        timeout: 10000
      })

      if (response.status === 200) {
        logInfo('✅ Webhook endpoint is accessible')
        return { accessible: true, status: response.status, data: response.data }
      } else {
        logInfo(`❌ Webhook endpoint returned status: ${response.status}`)
        return { accessible: false, status: response.status }
      }
    } catch (error) {
      logError('Webhook verification failed:', error.message)
      return { accessible: false, error: error.message }
    }
  }

  /**
   * Get webhook registration status
   */
  async getWebhookStatus(authToken) {
    try {
      logInfo('📋 Checking webhook registration status...')
      
      // Try to get bridge URL status
      const response = await axios.get(`${this.gatewayUrl}/bridge`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'REQUEST-ID': this.generateRequestId(),
          'TIMESTAMP': new Date().toISOString(),
          'X-CM-ID': 'sbx'
        }
      })

      logInfo('📊 Current webhook status:', response.data)
      return { success: true, data: response.data }
    } catch (error) {
      logError('Failed to get webhook status:', error.response?.data || error.message)
      return { success: false, error: error.response?.data || error.message }
    }
  }
}

module.exports = new ABDMWebhookRegistration()

/**
 * ABDM Handler for ABHA number generation, verification, and management
 * Handles all ABDM-related operations and API interactions
 */

const abdmService = require('../services/abdm-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { logError, logInfo } = require('../common/logging')
const {
  ABDMInitiateAadhaarRequest,
  ABDMValidator,
} = require('../models/abdm-model')

class ABDMHandler {
  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {Object} req - Request object
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(req) {
    try {
      const requestData = await req.json()

      // Validate request using model
      const validation = ABDMValidator.validateRequest(
        ABDMInitiateAadhaarRequest,
        requestData,
      )
      if (!validation.isValid) {
        return jsonResponse(
          ABDMValidator.formatValidationErrors(validation.errors),
          HttpStatusCode.BadRequest,
        )
      }

      const { aadhaar, mobile } = requestData

      logInfo(`Initiating ABHA creation by Aadhaar for mobile: ${mobile}`)

      const result = await abdmService.initiateAbhaCreationByAadhaar(
        aadhaar,
        mobile,
      )

      if (result.success) {
        // Trigger internal webhook for ABHA initiation
        this.triggerInternalWebhook('ABHA_INITIATION_STARTED', {
          txnId: result.txnId,
          mobile,
          timestamp: new Date().toISOString(),
          operation: 'initiate-aadhaar',
        })

        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in initiateAbhaCreationByAadhaar handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP for ABHA creation
   * @param {Object} req - Request object
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(req) {
    try {
      const { txnId, otp, type, mobile } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate type (mobile or aadhaar)
      if (type && !['mobile', 'aadhaar'].includes(type)) {
        return jsonResponse(
          'Invalid type. Must be "mobile" or "aadhaar".',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(
        `Verifying ${
          type || 'aadhaar'
        } OTP for ABHA creation with txnId: ${txnId}`,
      )

      // Use appropriate service method based on type
      const result =
        type === 'mobile'
          ? await abdmService.verifyMobileOtpForAbhaCreation(txnId, otp)
          : await abdmService.verifyOtpForAbhaCreation(txnId, otp, mobile)
      console.log(result, 'llllll')

      if (result.success) {
        // Trigger internal webhook for OTP verification success
        this.triggerInternalWebhook('ABHA_OTP_VERIFIED', {
          txnId: result.txnId,
          mobile,
          timestamp: new Date().toISOString(),
          operation: 'verify-otp',
          data: result.data,
        })

        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
            data: result.data,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpForAbhaCreation handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const result = await abdmService.getAbhaDetailsByNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(req) {
    try {
      const { mobile } = await req.json()

      // Validate required fields
      if (!mobile) {
        return jsonResponse(
          'Mobile number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate mobile format (10 digits)
      if (!/^\d{10}$/.test(mobile)) {
        return jsonResponse(
          'Invalid mobile number format. Must be 10 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      const result = await abdmService.getAbhaDetailsByMobile(mobile)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const result = await abdmService.verifyAbhaNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            isValid: result.isValid,
            status: result.status,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            isValid: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyAbhaNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {Object} req - Request object
   * @returns {Object} - Response
   */
  async resendOtp(req) {
    try {
      const { txnId } = await req.json()

      // Validate required fields
      if (!txnId) {
        return jsonResponse(
          'Transaction ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Resending OTP for txnId: ${txnId}`)

      const result = await abdmService.resendOtp(txnId)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in resendOtp handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByNumber(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyOtpAndFetchAbhaDetailsByNumber(
        txnId,
        otp,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByMobile(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyAbhaDetailsByMobile(txnId, otp)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify ABDM webhook endpoint
   * @param {Object} req - Request object
   * @returns {Object} - Verification response
   */
  async verifyWebhook(req) {
    try {
      logInfo('ABDM webhook verification request received')

      // Check for verification challenge parameter
      const challenge =
        req.query.get('challenge') || req.query.get('hub.challenge')
      const mode = req.query.get('mode') || req.query.get('hub.mode')
      const verifyToken =
        req.query.get('verify_token') || req.query.get('hub.verify_token')

      logInfo('Webhook verification parameters:', {
        challenge: challenge ? '***provided***' : 'missing',
        mode,
        verifyToken: verifyToken ? '***provided***' : 'missing',
      })

      // If challenge is provided, return it (Facebook/Meta style verification)
      if (challenge) {
        logInfo('Returning challenge for webhook verification')
        return new Response(challenge, {
          status: 200,
          headers: { 'Content-Type': 'text/plain' },
        })
      }

      // Default verification response
      return jsonResponse(
        {
          success: true,
          message: 'ABDM webhook endpoint is active',
          timestamp: new Date().toISOString(),
          endpoint: 'abdm/webhook',
          methods: ['GET', 'POST'],
          status: 'verified',
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logError('Error in webhook verification:', error)
      return jsonResponse(
        {
          success: false,
          error: 'Verification failed',
          message: error.message,
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Handle ABDM webhook notifications
   * @param {Object} req - Request object
   * @returns {Object} - Response acknowledging webhook receipt
   */
  async handleWebhook(req) {
    try {
      logInfo('🔔 Processing ABDM webhook notification')

      // Log all headers for debugging
      const headers = {}
      req.headers.forEach((value, key) => {
        headers[key] = key.toLowerCase().includes('auth')
          ? '***masked***'
          : value
      })
      logInfo('📋 Webhook headers:', headers)

      // Get webhook signature for verification (if ABDM provides one)
      const signature =
        req.headers.get('x-abdm-signature') ||
        req.headers.get('authorization') ||
        req.headers.get('signature')
      const timestamp =
        req.headers.get('x-timestamp') ||
        req.headers.get('timestamp') ||
        req.headers.get('x-abdm-timestamp')
      const requestId =
        req.headers.get('x-request-id') ||
        req.headers.get('request-id') ||
        req.headers.get('x-abdm-request-id')

      // Parse webhook payload
      const webhookData = await req.json()

      logInfo('📨 ABDM webhook received:', {
        requestId,
        timestamp,
        eventType:
          webhookData.eventType || webhookData.event || webhookData.type,
        hasSignature: !!signature,
        payloadKeys: Object.keys(webhookData),
        url: req.url,
        method: req.method,
      })

      // Log the full payload for debugging (be careful with sensitive data)
      logInfo('📄 Webhook payload:', JSON.stringify(webhookData, null, 2))

      // Process the webhook using the service
      const result = await abdmService.processWebhook(webhookData, {
        signature,
        timestamp,
        requestId,
      })

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            message: 'Webhook processed successfully',
            requestId: requestId,
            timestamp: new Date().toISOString(),
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            message: result.message || 'Webhook processing failed',
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in ABDM webhook handler:', error)
      return jsonResponse(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to process webhook',
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Trigger internal webhook for ABHA events
   * @param {string} eventType - Type of event
   * @param {Object} data - Event data
   */
  async triggerInternalWebhook(eventType, data) {
    try {
      logInfo(`🔔 Triggering internal webhook: ${eventType}`)

      const webhookPayload = {
        eventType,
        timestamp: new Date().toISOString(),
        requestId: `internal_${Date.now()}`,
        source: 'internal',
        data,
      }

      // Process the webhook internally
      const result = await abdmService.processWebhook(webhookPayload, {
        signature: null,
        timestamp: webhookPayload.timestamp,
        requestId: webhookPayload.requestId,
      })

      logInfo(`✅ Internal webhook processed: ${eventType}`, result)
      return result
    } catch (error) {
      logError(`❌ Error triggering internal webhook: ${eventType}`, error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = new ABDMHandler()

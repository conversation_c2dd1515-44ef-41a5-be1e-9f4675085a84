# ABDM Webhook Implementation Guide

## Overview

This guide explains how to implement and register webhooks for ABDM (Ayushman Bharat Digital Mission) integration in your EMR system. The webhook allows ABDM to send real-time notifications about various events related to ABHA numbers, consent management, and health record sharing.

## Webhook Endpoint

**URL**: `https://your-domain.com/api/abdm/webhook`
**Method**: `POST`
**Authentication**: Anonymous (signature-based verification)

## Supported Events

The webhook handles the following ABDM events:

### 1. ABHA Events
- `ABHA_CREATED` / `abha.created` - When a new ABHA number is created
- `ABHA_UPDATED` / `abha.updated` - When ABHA details are updated
- `ABHA_VERIFIED` / `abha.verified` - When ABHA number is verified

### 2. Consent Events
- `CONSENT_GRANTED` / `consent.granted` - When patient grants consent for data sharing
- `CONSENT_REVOKED` / `consent.revoked` - When patient revokes consent

### 3. Health Record Events
- `HEALTH_RECORD_SHARED` / `health_record.shared` - When health records are shared

## Webhook Payload Structure

### Request Headers
```
Content-Type: application/json
X-ABDM-Signature: <signature_hash>
X-Timestamp: <iso_timestamp>
X-Request-ID: <unique_request_id>
```

### Request Body
```json
{
  "eventType": "ABHA_CREATED",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_123456789",
  "data": {
    "abhaNumber": "12-3456-7890-1234",
    "abhaAddress": "john.doe@abdm",
    "mobile": "**********",
    "email": "<EMAIL>",
    "txnId": "txn_abc123"
  }
}
```

## Event-Specific Payloads

### ABHA Created Event
```json
{
  "eventType": "ABHA_CREATED",
  "data": {
    "abhaNumber": "12-3456-7890-1234",
    "abhaAddress": "john.doe@abdm",
    "mobile": "**********",
    "email": "<EMAIL>",
    "txnId": "txn_abc123"
  }
}
```

### Consent Granted Event
```json
{
  "eventType": "CONSENT_GRANTED",
  "data": {
    "consentId": "consent_123",
    "abhaNumber": "12-3456-7890-1234",
    "purpose": "CAREMGT",
    "dataTypes": ["Prescription", "DiagnosticReport"],
    "validFrom": "2024-01-15T00:00:00.000Z",
    "validTo": "2024-12-31T23:59:59.000Z"
  }
}
```

### Health Record Shared Event
```json
{
  "eventType": "HEALTH_RECORD_SHARED",
  "data": {
    "recordId": "record_456",
    "abhaNumber": "12-3456-7890-1234",
    "sharedWith": "hospital_123",
    "recordType": "Prescription",
    "sharedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "requestId": "req_123456789",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "INVALID_SIGNATURE",
  "message": "Webhook signature verification failed"
}
```

## Security

### Signature Verification
The webhook uses HMAC-SHA256 signature verification:

1. ABDM signs the payload using a shared secret
2. The signature is sent in the `X-ABDM-Signature` header
3. Your webhook verifies the signature before processing

### Environment Variables
Add to your `local.settings.json`:
```json
{
  "ABDM_WEBHOOK_SECRET": "your-abdm-webhook-secret-key"
}
```

## Implementation Details

### Files Modified/Created
1. `src/functions/abdm.js` - Added webhook endpoint
2. `src/handlers/abdm-handler.js` - Added webhook handler
3. `src/services/abdm-service.js` - Added webhook processing logic
4. `src/models/abdm-model.js` - Added webhook models
5. `local.settings.json` - Added webhook secret configuration

### Key Features
- **Signature Verification**: Ensures webhook authenticity
- **Event Routing**: Different handlers for different event types
- **Error Handling**: Comprehensive error handling and logging
- **Extensible**: Easy to add new event types
- **Logging**: Detailed logging for debugging and monitoring

## Testing the Webhook

### Using curl
```bash
curl -X POST https://your-domain.com/api/abdm/webhook \
  -H "Content-Type: application/json" \
  -H "X-ABDM-Signature: your_signature_hash" \
  -H "X-Timestamp: 2024-01-15T10:30:00.000Z" \
  -H "X-Request-ID: test_req_123" \
  -d '{
    "eventType": "ABHA_CREATED",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "requestId": "test_req_123",
    "data": {
      "abhaNumber": "12-3456-7890-1234",
      "abhaAddress": "test.user@abdm",
      "mobile": "**********"
    }
  }'
```

### Expected Response
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "requestId": "test_req_123",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Monitoring and Logging

The webhook implementation includes comprehensive logging:
- Incoming webhook requests
- Signature verification results
- Event processing status
- Error details for failed requests

Check your Azure Function logs for webhook activity.

## Next Steps

1. **Register the webhook URL with ABDM**
2. **Configure the webhook secret**
3. **Test with sample payloads**
4. **Monitor webhook activity**
5. **Implement business logic for each event type**

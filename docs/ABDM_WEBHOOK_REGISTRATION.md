# ABDM Webhook Registration Guide

## Overview

This guide explains how to register your webhook URL with ABDM (Ayushman Bharat Digital Mission) to receive real-time notifications about ABHA-related events.

## Prerequisites

1. **ABDM Integration Setup**: Ensure you have valid ABDM credentials
   - Client ID: `SBXID_009193`
   - Client Secret: `4df7c42b-46c0-4b1b-95b6-35c6a5317708`
   - Base URL: `https://abhasbx.abdm.gov.in/abha/api/v3`

2. **Webhook Endpoint**: Your webhook should be publicly accessible
   - URL: `https://your-domain.com/api/abdm/webhook`
   - Method: POST
   - Authentication: Anonymous (signature-based)

3. **SSL Certificate**: ABDM requires HTTPS endpoints

## Registration Methods

### Method 1: ABDM Developer Portal (Recommended)

1. **Login to ABDM Developer Portal**
   - Visit: https://sandbox.abdm.gov.in/
   - Login with your developer credentials

2. **Navigate to Webhook Configuration**
   - Go to "Integration" → "Webhooks"
   - Click "Add New Webhook"

3. **Configure Webhook Details**
   ```
   Webhook Name: EMR System Webhook
   Endpoint URL: https://your-domain.com/api/abdm/webhook
   Method: POST
   Content Type: application/json
   ```

4. **Select Events to Subscribe**
   - ✅ ABHA Created
   - ✅ ABHA Updated
   - ✅ ABHA Verified
   - ✅ Consent Granted
   - ✅ Consent Revoked
   - ✅ Health Record Shared

5. **Configure Security**
   - Enable signature verification
   - Generate webhook secret key
   - Save the secret key in your environment variables

6. **Test the Webhook**
   - Use the portal's test feature
   - Send sample events to verify connectivity

### Method 2: API-Based Registration

If the developer portal doesn't support webhook registration, use the API approach:

#### Step 1: Get Authentication Token
```bash
curl -X POST https://abhasbx.abdm.gov.in/abha/api/v3/auth/token \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "SBXID_009193",
    "clientSecret": "4df7c42b-46c0-4b1b-95b6-35c6a5317708"
  }'
```

#### Step 2: Register Webhook (Hypothetical Endpoint)
```bash
curl -X POST https://abhasbx.abdm.gov.in/abha/api/v3/webhooks \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://your-domain.com/api/abdm/webhook",
    "events": [
      "ABHA_CREATED",
      "ABHA_UPDATED", 
      "ABHA_VERIFIED",
      "CONSENT_GRANTED",
      "CONSENT_REVOKED",
      "HEALTH_RECORD_SHARED"
    ],
    "secret": "your-webhook-secret-key"
  }'
```

### Method 3: Contact ABDM Support

If neither method works:

1. **Email ABDM Support**
   - Email: <EMAIL>
   - Subject: "Webhook Registration Request - [Your Organization]"

2. **Provide Required Information**
   ```
   Organization Name: [Your Organization]
   Client ID: SBXID_009193
   Webhook URL: https://your-domain.com/api/abdm/webhook
   Events Required: ABHA_CREATED, ABHA_UPDATED, ABHA_VERIFIED, 
                   CONSENT_GRANTED, CONSENT_REVOKED, HEALTH_RECORD_SHARED
   Contact Person: [Name and Email]
   ```

3. **Follow Up**
   - ABDM support will provide webhook secret
   - They may require additional verification

## Configuration Steps

### 1. Update Environment Variables

Add the webhook secret provided by ABDM to your configuration:

**local.settings.json**
```json
{
  "Values": {
    "ABDM_WEBHOOK_SECRET": "secret-provided-by-abdm"
  }
}
```

**Azure Function App Settings**
```bash
az functionapp config appsettings set \
  --name your-function-app \
  --resource-group your-resource-group \
  --settings ABDM_WEBHOOK_SECRET="secret-provided-by-abdm"
```

### 2. Verify Webhook Endpoint

Test your webhook endpoint:

```bash
curl -X POST https://your-domain.com/api/abdm/webhook \
  -H "Content-Type: application/json" \
  -H "X-ABDM-Signature: test_signature" \
  -H "X-Timestamp: $(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)" \
  -H "X-Request-ID: test_$(date +%s)" \
  -d '{
    "eventType": "ABHA_CREATED",
    "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
    "requestId": "test_'$(date +%s)'",
    "data": {
      "abhaNumber": "12-3456-7890-1234",
      "abhaAddress": "test.user@abdm",
      "mobile": "9876543210"
    }
  }'
```

### 3. Monitor Webhook Activity

Check Azure Function logs:
```bash
az functionapp log tail --name your-function-app --resource-group your-resource-group
```

## Webhook URL Format

Your webhook URL should follow this format:

**Development Environment**
```
https://your-dev-function-app.azurewebsites.net/api/abdm/webhook
```

**Production Environment**
```
https://your-prod-function-app.azurewebsites.net/api/abdm/webhook
```

**Custom Domain (Recommended)**
```
https://api.your-domain.com/abdm/webhook
```

## Security Considerations

### 1. HTTPS Only
- ABDM only supports HTTPS webhooks
- Ensure valid SSL certificate

### 2. Signature Verification
- Always verify webhook signatures
- Use the secret provided by ABDM
- Reject requests with invalid signatures

### 3. IP Whitelisting (Optional)
If ABDM provides IP ranges, whitelist them:
```
# Example ABDM IP ranges (verify with ABDM)
103.x.x.x/24
202.x.x.x/24
```

### 4. Rate Limiting
Implement rate limiting to prevent abuse:
```javascript
// Example rate limiting (implement as needed)
const rateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}
```

## Troubleshooting

### Common Issues

1. **Webhook Not Receiving Events**
   - Verify URL is publicly accessible
   - Check HTTPS certificate validity
   - Confirm webhook is registered with ABDM

2. **Signature Verification Failures**
   - Verify webhook secret is correct
   - Check signature calculation method
   - Ensure payload is not modified

3. **Timeout Errors**
   - Webhook should respond within 30 seconds
   - Implement async processing for heavy operations
   - Return 200 OK immediately, process later

### Debug Steps

1. **Check Function Logs**
   ```bash
   az functionapp log tail --name your-function-app
   ```

2. **Test Webhook Manually**
   ```bash
   curl -X POST https://your-webhook-url \
     -H "Content-Type: application/json" \
     -d '{"test": "payload"}'
   ```

3. **Verify Network Connectivity**
   ```bash
   nslookup your-domain.com
   curl -I https://your-domain.com/api/abdm/webhook
   ```

## Support and Documentation

### ABDM Resources
- Developer Portal: https://sandbox.abdm.gov.in/
- API Documentation: https://sandbox.abdm.gov.in/docs
- Support Email: <EMAIL>

### Additional Help
- ABDM Developer Community: [Link if available]
- Technical Documentation: [Link to official docs]
- FAQ: [Link to frequently asked questions]

## Troubleshooting Webhook Issues

### Issue: Webhook Not Receiving Events After ABHA Creation

**Possible Causes:**
1. **URL Registration Issues**
   - Double slash in URL (`//api` instead of `/api`)
   - Wrong endpoint registered
   - Token expired during registration

2. **ABDM Event Triggers**
   - Bridge URL might be for different events
   - ABHA creation might not trigger webhooks immediately
   - Sandbox vs Production environment differences

3. **Webhook Verification**
   - ABDM might verify endpoint before sending events
   - Endpoint not responding to verification requests

**Solutions:**

1. **Re-register with Correct URL**
   ```bash
   # Use the script provided
   chmod +x scripts/register-abdm-webhook.sh
   ./scripts/register-abdm-webhook.sh
   ```

2. **Test Webhook Connectivity**
   ```bash
   chmod +x scripts/test-webhook-connectivity.sh
   ./scripts/test-webhook-connectivity.sh
   ```

3. **Check Function Logs**
   ```bash
   # Monitor your Azure Function logs
   func logs
   ```

4. **Verify ngrok Setup**
   ```bash
   # Check ngrok status
   curl http://localhost:4040/api/tunnels

   # Restart ngrok if needed
   ngrok http 7071
   ```

### Common ABDM Webhook Patterns

Based on ABDM documentation patterns, webhooks might be triggered by:

1. **Health Information Provider (HIP) Events**
   - Patient consent granted
   - Health records shared
   - Data requests

2. **Health Information User (HIU) Events**
   - Data received
   - Consent status changes

3. **ABHA Events** (Less common for webhooks)
   - ABHA creation might not trigger immediate webhooks
   - Webhooks might be for consent/data sharing only

### Alternative Approaches

If webhooks aren't working for ABHA creation:

1. **Polling Approach**
   - Periodically check ABHA status
   - Use ABDM APIs to verify creation

2. **Event-Driven Architecture**
   - Trigger events from your ABHA creation flow
   - Use internal webhooks/events

3. **Database Triggers**
   - Monitor your database for ABHA records
   - Trigger actions on new records

## Next Steps

1. ✅ Implement webhook endpoint (completed)
2. ✅ Add webhook verification (completed)
3. ✅ Add enhanced logging (completed)
4. ⏳ Re-register webhook with correct URL
5. ⏳ Test webhook connectivity
6. ⏳ Monitor function logs during ABHA creation
7. ⏳ Contact ABDM support if issues persist
